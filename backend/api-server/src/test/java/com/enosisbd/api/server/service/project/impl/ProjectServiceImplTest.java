package com.enosisbd.api.server.service.project.impl;

import com.enosisbd.api.server.dto.ProjectDto;
import com.enosisbd.api.server.dto.ProjectProcessingResultDto;
import com.enosisbd.api.server.dto.SheetBatchDataDto;
import com.enosisbd.api.server.dto.SpreadsheetBatchResponseDto;
import com.enosisbd.api.server.dto.SubmoduleExtractionResultDto;
import com.enosisbd.api.server.entity.Project;
import com.enosisbd.api.server.repository.ProjectRepository;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import com.enosisbd.api.server.service.entitySharing.EntitySharingService;
import com.enosisbd.api.server.service.googlesheets.GoogleSheetsService;
import com.enosisbd.api.server.service.module.ModuleService;
import com.enosisbd.api.server.service.submodule.SubModuleService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProjectServiceImplTest {

    @Mock
    private ProjectRepository projectRepository;

    @Mock
    private EntitySharingService entitySharingService;

    @Mock
    private AuthorizationService authorizationService;

    @Mock
    private GoogleSheetsService googleSheetsService;

    @Mock
    private ModuleService moduleService;

    @Mock
    private SubModuleService subModuleService;

    @InjectMocks
    private ProjectServiceImpl projectService;

    @Captor
    private ArgumentCaptor<Project> projectCaptor;

    private ProjectDto projectDto;
    private Project project;
    private final String TEST_SHEET_URL = "https://docs.google.com/spreadsheets/d/1234567890abcdef/edit#gid=0";
    private final String TEST_SHEET_ID = "1234567890abcdef";
    private final String TEST_USER_EMAIL = "<EMAIL>";

    @BeforeEach
    void setUp() {
        // Create project DTO
        projectDto = new ProjectDto();
        projectDto.setName("Test Project");
        projectDto.setDescription("Test Description");
        projectDto.setGoogleSheetUrl(TEST_SHEET_URL);
        projectDto.setSubmoduleColumn("A");
        projectDto.setSubmoduleStartRow(2);

        // Create project entity
        project = new Project();
        project.setId(1L);
        project.setName("Test Project");
        project.setDescription("Test Description");
        project.setGoogleSheetUrl(TEST_SHEET_URL);
        project.setSubmoduleColumn("A");
        project.setSubmoduleStartRow(2);
    }

    @Test
    void add_ShouldProcessGoogleSheetAndSaveProjectWithSheetId() throws IOException, GeneralSecurityException {
        // Arrange
        projectDto.setId(1L); // Set the ID so it's not null during processing
        when(projectRepository.save(any(Project.class))).thenReturn(project);
        when(authorizationService.getCurrentUsername()).thenReturn(TEST_USER_EMAIL);
        when(googleSheetsService.extractSheetId(TEST_SHEET_URL)).thenReturn(TEST_SHEET_ID);

        // Mock batch response
        SheetBatchDataDto sheetBatchData = SheetBatchDataDto.builder()
                .sheetName("Sheet1")
                .sheetId(1)
                .data(new ArrayList<>())
                .dataRetrieved(true)
                .build();

        SpreadsheetBatchResponseDto batchResponse = SpreadsheetBatchResponseDto.builder()
                .spreadsheetId(TEST_SHEET_ID)
                .sheets(List.of(sheetBatchData))
                .totalSheets(1)
                .successfulSheets(1)
                .allSheetsProcessed(true)
                .errorMessages(new ArrayList<>())
                .build();

        when(googleSheetsService.getAllSheetsDataBatch(TEST_SHEET_ID, TEST_USER_EMAIL, null)).thenReturn(batchResponse);
        when(googleSheetsService.columnLetterToIndex("A")).thenReturn(0);

        // Mock the extractSubmodulesWithValidation method
        SubmoduleExtractionResultDto extractionResult = SubmoduleExtractionResultDto.builder()
                .submodules(new ArrayList<>())
                .validationIssues(new ArrayList<>())
                .build();
        when(googleSheetsService.extractSubmodulesWithValidation(any(), anyInt(), anyInt(), anyString(), anyString(), any()))
                .thenReturn(extractionResult);

        when(projectRepository.findById(1L)).thenReturn(Optional.of(project));

        // Act
        ProjectProcessingResultDto result = projectService.add(projectDto);

        // Assert
        verify(projectRepository, times(2)).save(projectCaptor.capture());

        // First save should be the initial project
        Project initialSavedProject = projectCaptor.getAllValues().get(0);
        assertEquals("Test Project", initialSavedProject.getName());
        assertEquals(TEST_SHEET_URL, initialSavedProject.getGoogleSheetUrl());

        // Second save should include the Google Sheet ID
        Project updatedProject = projectCaptor.getAllValues().get(1);
        assertEquals(TEST_SHEET_ID, updatedProject.getGoogleSheetId());

        // Result DTO should have the project data
        assertNotNull(result);
        assertNotNull(result.getProject());
        assertEquals(TEST_SHEET_ID, result.getProject().getGoogleSheetId());

        // Verify service calls - should use batch API instead of individual calls
        verify(googleSheetsService).extractSheetId(TEST_SHEET_URL);
        verify(authorizationService).getCurrentUsername();
        verify(googleSheetsService).getAllSheetsDataBatch(TEST_SHEET_ID, TEST_USER_EMAIL, null);
        verify(googleSheetsService).columnLetterToIndex("A");
    }

    @Test
    void add_ShouldNotProcessGoogleSheetWhenUrlIsEmpty() throws IOException, GeneralSecurityException {
        // Arrange
        projectDto.setGoogleSheetUrl("");
        when(projectRepository.save(any(Project.class))).thenReturn(project);
        // Mock extractSheetId to return something even for empty URL to avoid the call in convertToEntity
        when(googleSheetsService.extractSheetId("")).thenReturn("");

        // Act
        ProjectProcessingResultDto result = projectService.add(projectDto);

        // Assert
        verify(projectRepository, times(1)).save(any(Project.class));
        // The extractSheetId might be called in convertToEntity, so we can't verify it's never called
        verify(authorizationService, never()).getCurrentUsername();
        verify(googleSheetsService, never()).getAllSheetsDataBatch(anyString(), anyString(), anyString());

        assertNotNull(result);
        assertNotNull(result.getProject());
        assertEquals("Test Project", result.getProject().getName());
    }
}
