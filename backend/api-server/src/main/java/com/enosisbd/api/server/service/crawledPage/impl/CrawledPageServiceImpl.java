package com.enosisbd.api.server.service.crawledPage.impl;

import com.enosisbd.api.server.dto.CrawledPageDto;
import com.enosisbd.api.server.dto.CrawledPageSummaryDto;
import com.enosisbd.api.server.entity.CrawledPage;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.repository.CrawledPageRepository;
import com.enosisbd.api.server.repository.ProjectRepository;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import com.enosisbd.api.server.service.crawledPage.CrawledPageService;
import com.enosisbd.api.server.service.entitySharing.EntitySharingService;
import com.enosisbd.api.server.service.mapper.EntityDtoMapperService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CrawledPageServiceImpl implements CrawledPageService {
    private final CrawledPageRepository repository;
    private final ProjectRepository projectRepository;
    private final EntitySharingService entitySharingService;
    private final AuthorizationService authorizationService;
    private final EntityDtoMapperService entityDtoMapperService;

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<CrawledPageDto> getById(Long id) {
        // Access check is handled by the @RequiresEntityAccess annotation
        return repository.findByIdJoined(id)
                .map(entityDtoMapperService::convertToDto);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public CrawledPageDto add(CrawledPageDto dto) {
        CrawledPage entity = entityDtoMapperService.convertToEntity(dto);

        // Set creator
        entity.setCreatedBy(authorizationService.getCurrentUsername());

        Long projectId = dto.getProjectId();
        if (projectId != null) {
            // Check if user has access to the project
            var project = projectRepository
                    .findById(projectId)
                    .orElseThrow(() -> BadRequestRestException.with("Project not found with ID: " + projectId));

            if (!entitySharingService.hasAccess(project, EntityType.PROJECT)) {
                throw new BadRequestRestException("You don't have access to this project");
            }

            entity.setProject(project);
        }

        repository.save(entity);
        return entityDtoMapperService.convertToDto(entity);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<CrawledPageDto> update(CrawledPageDto dto) {
        var maybe = repository.findById(dto.getId());
        if (maybe.isEmpty()) return Optional.empty();

        CrawledPage existingEntity = maybe.get();
        CrawledPage entity = entityDtoMapperService.convertToEntity(existingEntity, dto);

        // Access check is handled by the @RequiresEntityAccess annotation

        // Check if project ID is valid and matches the existing entity
        Long projectId = dto.getProjectId();
        if (projectId != null && !projectId.equals(existingEntity.getProjectId())) {
            throw new BadRequestRestException("Cannot change the project of a crawled page");
        }

        // Set the project from the existing entity
        entity.setProject(existingEntity.getProject());

        repository.save(entity);
        return Optional.of(entityDtoMapperService.convertToDto(entity));
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<Boolean> delete(Long id) {
        Optional<CrawledPage> crawledPage = repository.findById(id);
        if (crawledPage.isEmpty()) {
            return Optional.empty();
        }

        // Access check is handled by the @RequiresEntityAccess annotation

        // Delete the crawled page
        repository.delete(crawledPage.get());
        return Optional.of(true);
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public List<CrawledPageDto> findByProjectId(Long projectId) {
        // Get all crawled pages for the project
        List<CrawledPage> allCrawledPages = repository.findByProjectIdJoined(projectId);
        if (allCrawledPages.isEmpty()) {
            return List.of();
        }

        // Convert to DTOs
        return allCrawledPages.stream()
                .map(entityDtoMapperService::convertToDto)
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public List<CrawledPageDto> saveOrUpdateBatch(List<CrawledPageDto> dtoList) {
        if (dtoList.isEmpty()) {
            return List.of();
        }

        Long projectId = dtoList.stream()
                .map(CrawledPageDto::getProjectId)
                .filter(Objects::nonNull)
                .findFirst()
                .orElseThrow(() -> new BadRequestRestException("No projectId found in the batch"));

        // Check if user has access to the project
        var project = projectRepository
                .findById(projectId)
                .orElseThrow(() -> BadRequestRestException.with("Project not found with ID: " + projectId));

        if (!entitySharingService.hasAccess(project, EntityType.PROJECT)) {
            throw new BadRequestRestException("You don't have access to this project");
        }

        List<CrawledPage> existingEntities = repository.findByProjectIdJoined(projectId);

        /// Update this values for previous crawledpages which are not updated,
        /// since now we are not fetched DOMJson when just display crawled pages in the Custom Drawer which caused existing DOMJson value null
        updateDOMJsonWithNullValue(dtoList, existingEntities);

        List<Long> newListIds = dtoList.stream()
                .map(CrawledPageDto::getId)
                .filter(Objects::nonNull)
                .toList();

        List<Long> idsToDelete = existingEntities.stream()
                .map(CrawledPage::getId)
                .filter(id -> !newListIds.contains(id))
                .toList();

        idsToDelete.forEach(repository::deleteById);

        return dtoList.stream()
                .map(dto -> {
                    if (dto.getId() != null && repository.existsById(dto.getId())) {
                        return update(dto).orElseThrow(() ->
                                new BadRequestRestException("Failed to update crawled page with id: " + dto.getId()));
                    } else {
                        return add(dto);
                    }
                })
                .toList();
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public List<CrawledPageSummaryDto> findByProjectIdSummary(Long projectId) {
        // Get only required fields for the project without DOMJson for performance
        List<Object[]> summaryFields = repository.findByProjectIdSummaryFields(projectId);
        if (summaryFields.isEmpty()) {
            return List.of();
        }

        // Convert to summary DTOs (without DOMJson)
        return summaryFields.stream()
                .map(this::convertToSummaryDtoFromFields)
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public List<CrawledPageDto> findByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return List.of();
        }

        // Get crawled pages by IDs with DOMJson
        List<CrawledPage> crawledPages = repository.findByIds(ids);

        // Convert to DTOs
        return crawledPages.stream()
                .map(entityDtoMapperService::convertToDto)
                .collect(Collectors.toList());
    }

    private CrawledPageSummaryDto convertToSummaryDtoFromFields(Object[] fields) {
        CrawledPageSummaryDto dto = new CrawledPageSummaryDto();
        // Fields order: id, createdAt, updatedAt, createdBy, lastModifiedBy, pageName, pageUrl, crawlOption, projectId
        dto.setId((Long) fields[0]);
        dto.setCreatedAt((java.time.LocalDateTime) fields[1]);
        dto.setUpdatedAt((java.time.LocalDateTime) fields[2]);
        dto.setCreatedBy((String) fields[3]);
        dto.setPageName((String) fields[5]);
        dto.setPageUrl((String) fields[6]);
        dto.setCrawlOption((com.enosisbd.api.server.model.CrawlOption) fields[7]);
        dto.setProjectId((Long) fields[8]);
        // Note: domJson is intentionally excluded for performance
        return dto;
    }



    private void updateDOMJsonWithNullValue(List<CrawledPageDto> pagesFromRequest, List<CrawledPage> existingPages) {
        Map<Long, Object> bDomMap = existingPages.stream()
                .collect(Collectors.toMap(CrawledPage::getId, CrawledPage::getDomJson));

        pagesFromRequest.forEach(a -> {
            if (a.getDomJson() == null && bDomMap.containsKey(a.getId())) {
                a.setDomJson(bDomMap.get(a.getId()));
            }
        });
    }
}
