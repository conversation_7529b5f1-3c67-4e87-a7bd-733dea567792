package com.enosisbd.api.server.dto;

import com.google.api.services.sheets.v4.model.GridRange;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO for representing individual sheet data in a batch response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SheetBatchDataDto {
    /**
     * The name/title of the sheet
     */
    private String sheetName;
    
    /**
     * The sheet ID (numeric identifier)
     */
    private Integer sheetId;
    
    /**
     * The sheet data as a list of rows, where each row is a list of cell values
     */
    private List<List<Object>> data;
    
    /**
     * Whether the sheet data was successfully retrieved
     */
    private boolean dataRetrieved;
    
    /**
     * Error message if data retrieval failed
     */
    private String errorMessage;

    /**
     * Merge ranges for the sheet
     */
    private List<GridRange> merges;
}
