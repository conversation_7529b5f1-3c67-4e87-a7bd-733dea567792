package com.enosisbd.api.server.service.testSuite.impl;

import com.enosisbd.api.server.dto.TestSuiteDto;
import com.enosisbd.api.server.entity.TestSuite;
import com.enosisbd.api.server.entity.Version;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.repository.TestCaseRepository;
import com.enosisbd.api.server.repository.TestSuiteRepository;
import com.enosisbd.api.server.repository.VersionRepository;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import com.enosisbd.api.server.service.entitySharing.EntitySharingService;
import com.enosisbd.api.server.service.mapper.EntityDtoMapperService;
import com.enosisbd.api.server.service.testSuite.TestSuiteService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class TestSuiteServiceImpl implements TestSuiteService {
    private final TestSuiteRepository repository;
    private final VersionRepository versionRepository;
    private final TestCaseRepository testCaseRepository;
    private final EntitySharingService entitySharingService;
    private final AuthorizationService authorizationService;
    private final EntityDtoMapperService entityDtoMapperService;

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public boolean existsById(Long id) {
        Optional<TestSuite> testSuite = repository.findById(id);
        if (testSuite.isEmpty()) {
            return false;
        }

        return entitySharingService.hasAccess(testSuite.get(), EntityType.TEST_SUITE);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<TestSuiteDto> getById(Long id) {
        // Access check is handled by the @RequiresEntityAccess annotation
        return repository.findByIdJoined(id)
                .map(entityDtoMapperService::convertToDto);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public TestSuiteDto add(TestSuiteDto dto) {
        validateTestSuite(dto);
        TestSuite entity = entityDtoMapperService.convertToEntity(dto);

        // Set creator
        entity.setCreatedBy(authorizationService.getCurrentUsername());

        Long versionId = dto.getVersionId();
        if (versionId != null) {
            // Check if user has access to the version
            Version version = versionRepository
                    .findById(versionId)
                    .orElseThrow(() -> BadRequestRestException.with("Version not found with ID: " + versionId));

            if (!entitySharingService.hasAccess(version, EntityType.VERSION)) {
                throw new BadRequestRestException("You don't have access to this version");
            }

            entity.setVersionEntity(version);
        }

        repository.save(entity);
        return entityDtoMapperService.convertToDto(entity);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<TestSuiteDto> update(TestSuiteDto dto) {
        var maybe = repository.findById(dto.getId());
        if (maybe.isEmpty())
            return Optional.empty();

        TestSuite entity = entityDtoMapperService.convertToEntity(maybe.get(), dto);

        // Access check is handled by the @RequiresEntityAccess annotation

        // Check if version needs to be updated
        if (dto.getVersionId() != null && !Objects.equals(dto.getVersionId(), entity.getVersionId())) {
            // Check if user has access to the version
            Version version = versionRepository
                    .findById(dto.getVersionId())
                    .orElseThrow(() -> BadRequestRestException.with("Version not found with ID: " + dto.getVersionId()));
            entity.setVersionEntity(version);
        }

        repository.save(entity);
        return Optional.of(entityDtoMapperService.convertToDto(entity));
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<Boolean> delete(Long id) {
        Optional<TestSuite> testSuite = repository.findById(id);
        if (testSuite.isEmpty()) {
            return Optional.empty();
        }

        // Access check is handled by the @RequiresEntityAccess annotation

        // Delete the test suite
        repository.delete(testSuite.get());
        return Optional.of(true);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public List<TestSuiteDto> findByVersionId(Long versionId) {
        // Get all test suites for the version
        List<TestSuite> allTestSuites = repository.findByVersionIdJoined(versionId);
        if (allTestSuites.isEmpty()) {
            return List.of();
        }

        // Convert to DTOs using the centralized mapper
        return allTestSuites.stream()
                .map(entityDtoMapperService::convertToDto)
                .sorted((a, b) -> a.getTitle().compareTo(b.getTitle()))
                .toList();
    }

    private void validateTestSuite(TestSuiteDto dto) {
        if (dto.getTitle() == null || dto.getTitle().trim().isEmpty()) {
            throw new BadRequestRestException("Title cannot be empty");
        }

        if (dto.getTitle().length() < 3 || dto.getTitle().length() > 255) {
            throw new BadRequestRestException("Title must be between 3 and 255 characters");
        }
    }
}
