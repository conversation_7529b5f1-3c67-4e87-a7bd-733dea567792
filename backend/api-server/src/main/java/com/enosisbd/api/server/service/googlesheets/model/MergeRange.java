package com.enosisbd.api.server.service.googlesheets.model;

import lombok.Getter;

/**
 * Represents a merged cell range in Google Sheets
 * Contains the boundaries of a merged cell area
 */
public class MergeRange {
    @Getter
    private final int startRowIndex;
    @Getter
    private final int endRowIndex;
    private final int startColumnIndex;
    private final int endColumnIndex;

    public MergeRange(int startRowIndex, int endRowIndex, int startColumnIndex, int endColumnIndex) {
        this.startRowIndex = startRowIndex;
        this.endRowIndex = endRowIndex;
        this.startColumnIndex = startColumnIndex;
        this.endColumnIndex = endColumnIndex;
    }

    /**
     * Check if a specific cell (row, column) is within this merge range
     *
     * @param rowIndex The row index (0-based)
     * @param columnIndex The column index (0-based)
     * @return true if the cell is within this merge range
     */
    public boolean containsCell(int rowIndex, int columnIndex) {
        return rowIndex >= startRowIndex && rowIndex < endRowIndex &&
               columnIndex >= startColumnIndex && columnIndex < endColumnIndex;
    }

    /**
     * Check if this merge range affects the specified column
     *
     * @param columnIndex The column index (0-based)
     * @return true if this merge range includes the specified column
     */
    public boolean affectsColumn(int columnIndex) {
        return columnIndex >= startColumnIndex && columnIndex < endColumnIndex;
    }

    @Override
    public String toString() {
        return String.format("MergeRange{rows: %d-%d, columns: %d-%d}", 
                startRowIndex, endRowIndex - 1, startColumnIndex, endColumnIndex - 1);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        MergeRange that = (MergeRange) obj;
        return startRowIndex == that.startRowIndex &&
               endRowIndex == that.endRowIndex &&
               startColumnIndex == that.startColumnIndex &&
               endColumnIndex == that.endColumnIndex;
    }

    @Override
    public int hashCode() {
        int result = startRowIndex;
        result = 31 * result + endRowIndex;
        result = 31 * result + startColumnIndex;
        result = 31 * result + endColumnIndex;
        return result;
    }
}
