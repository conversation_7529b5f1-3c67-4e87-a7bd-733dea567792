package com.enosisbd.api.server.service.googlesheets.model;

import java.util.List;

/**
 * Container class that holds both sheet cell data and merge range information
 * This allows for proper handling of merged cells during submodule extraction
 */
public class SheetDataWithMerges {
    private final List<List<Object>> cellData;
    private final List<MergeRange> mergeRanges;

    public SheetDataWithMerges(List<List<Object>> cellData, List<MergeRange> mergeRanges) {
        this.cellData = cellData;
        this.mergeRanges = mergeRanges;
    }

    public List<List<Object>> getCellData() {
        return cellData;
    }

    public List<MergeRange> getMergeRanges() {
        return mergeRanges;
    }

    /**
     * Find the merge range that contains the specified cell
     *
     * @param rowIndex The row index (0-based)
     * @param columnIndex The column index (0-based)
     * @return The MergeRange containing this cell, or null if the cell is not merged
     */
    public MergeRange findMergeRangeContaining(int rowIndex, int columnIndex) {
        for (MergeRange mergeRange : mergeRanges) {
            if (mergeRange.containsCell(rowIndex, columnIndex)) {
                return mergeRange;
            }
        }
        return null;
    }

    /**
     * Check if a specific cell is part of a merged range
     *
     * @param rowIndex The row index (0-based)
     * @param columnIndex The column index (0-based)
     * @return true if the cell is part of a merged range
     */
    public boolean isCellMerged(int rowIndex, int columnIndex) {
        return findMergeRangeContaining(rowIndex, columnIndex) != null;
    }

    /**
     * Get all merge ranges that affect the specified column
     *
     * @param columnIndex The column index (0-based)
     * @return List of merge ranges that include the specified column
     */
    public List<MergeRange> getMergeRangesForColumn(int columnIndex) {
        return mergeRanges.stream()
                .filter(range -> range.affectsColumn(columnIndex))
                .toList();
    }
}
